{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeX5offlineDebugResources-77:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,702,783,864,940,1031,1124,1194,1258,1342,1425,1490,1554,1617,1687,1807,1925,2044,2116,2200,2269,2338,2432,2526,2591,2657,2710,2770,2818,2879,2944,3014,3079,3145,3209,3269,3334,3386,3448,3524,3600,3655", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,51,61,75,75,54,66", "endOffsets": "318,509,697,778,859,935,1026,1119,1189,1253,1337,1420,1485,1549,1612,1682,1802,1920,2039,2111,2195,2264,2333,2427,2521,2586,2652,2705,2765,2813,2874,2939,3009,3074,3140,3204,3264,3329,3381,3443,3519,3595,3650,3717"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,418,609,5785,5866,5947,6023,6114,6207,6277,6341,6425,6508,6573,6637,6700,6770,6890,7008,7127,7199,7283,7352,7421,7515,7609,7674,8434,8487,8547,8595,8656,8721,8791,8856,8922,8986,9046,9111,9163,9225,9301,9377,9432", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,51,61,75,75,54,66", "endOffsets": "413,604,792,5861,5942,6018,6109,6202,6272,6336,6420,6503,6568,6632,6695,6765,6885,7003,7122,7194,7278,7347,7416,7510,7604,7669,7735,8482,8542,8590,8651,8716,8786,8851,8917,8981,9041,9106,9158,9220,9296,9372,9427,9494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "963,1069,1168,1279,1365,1467,1584,1665,1742,1834,1928,2024,2126,2235,2329,2430,2524,2616,2709,2792,2903,3007,3106,3216,3318,3417,3583,21242", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "1064,1163,1274,1360,1462,1579,1660,1737,1829,1923,2019,2121,2230,2324,2425,2519,2611,2704,2787,2898,3002,3101,3211,3313,3412,3578,3680,21320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4650,4737,4822,4928,5008,5094,5195,5299,5393,5497,5584,5693,5794,5901,6018,6098,6202", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4645,4732,4817,4923,5003,5089,5190,5294,5388,5492,5579,5688,5789,5896,6013,6093,6197,6296"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9885,10005,10127,10241,10360,10459,10560,10678,10811,10931,11079,11166,11267,11361,11460,11576,11703,11809,11944,12077,12208,12383,12509,12628,12749,12871,12966,13063,13183,13317,13422,13525,13630,13761,13896,14004,14107,14184,14280,14376,14480,14567,14652,14758,14838,14924,15025,15129,15223,15327,15414,15523,15624,15731,15848,15928,16032", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "10000,10122,10236,10355,10454,10555,10673,10806,10926,11074,11161,11262,11356,11455,11571,11698,11804,11939,12072,12203,12378,12504,12623,12744,12866,12961,13058,13178,13312,13417,13520,13625,13756,13891,13999,14102,14179,14275,14371,14475,14562,14647,14753,14833,14919,15020,15124,15218,15322,15409,15518,15619,15726,15843,15923,16027,16126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1047,1111,1196,1264,1328,1415,1479,1543,1602,1674,1738,1792,1911,1971,2032,2086,2159,2292,2376,2453,2546,2626,2719,2857,2937,3016,3142,3230,3309,3364,3415,3481,3554,3633,3704,3783,3856,3931,4005,4077,4190,4278,4355,4446,4538,4612,4686,4777,4831,4913,4982,5065,5151,5213,5277,5340,5408,5511,5614,5711,5812,5871,5926,6007,6096,6173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "261,341,420,505,597,684,783,900,982,1042,1106,1191,1259,1323,1410,1474,1538,1597,1669,1733,1787,1906,1966,2027,2081,2154,2287,2371,2448,2541,2621,2714,2852,2932,3011,3137,3225,3304,3359,3410,3476,3549,3628,3699,3778,3851,3926,4000,4072,4185,4273,4350,4441,4533,4607,4681,4772,4826,4908,4977,5060,5146,5208,5272,5335,5403,5506,5609,5706,5807,5866,5921,6002,6091,6168,6246"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,3765,3845,3924,4009,4101,4917,5016,5133,5661,5721,9499,9584,9821,16131,16218,16282,16346,16405,16477,16541,16595,16714,16774,16835,16889,16962,17185,17269,17346,17439,17519,17612,17750,17830,17909,18035,18123,18202,18257,18308,18374,18447,18526,18597,18676,18749,18824,18898,18970,19083,19171,19248,19339,19431,19505,19579,19670,19724,19806,19875,19958,20044,20106,20170,20233,20301,20404,20507,20604,20705,20764,20994,21325,21414,21563", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "958,3840,3919,4004,4096,4183,5011,5128,5210,5716,5780,9579,9647,9880,16213,16277,16341,16400,16472,16536,16590,16709,16769,16830,16884,16957,17090,17264,17341,17434,17514,17607,17745,17825,17904,18030,18118,18197,18252,18303,18369,18442,18521,18592,18671,18744,18819,18893,18965,19078,19166,19243,19334,19426,19500,19574,19665,19719,19801,19870,19953,20039,20101,20165,20228,20296,20399,20502,20599,20700,20759,20814,21070,21409,21486,21636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "56,57,58,59,60,61,62,258", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4188,4283,4385,4483,4586,4692,4797,21949", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "4278,4380,4478,4581,4687,4792,4912,22045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "17095", "endColumns": "89", "endOffsets": "17180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,263,337,419,490,580,672", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "124,191,258,332,414,485,575,667,744"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7740,7814,7881,7948,8022,8104,8175,8265,8357", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "7809,7876,7943,8017,8099,8170,8260,8352,8429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,225", "endColumns": "79,89,88", "endOffsets": "130,220,309"}, "to": {"startLines": "50,262,263", "startColumns": "4,4,4", "startOffsets": "3685,22320,22410", "endColumns": "79,89,88", "endOffsets": "3760,22405,22494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,279,359,455,550,632,710,801,892,976,1058,1143,1215,1301,1376,1451,1523,1600,1671", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "274,354,450,545,627,705,796,887,971,1053,1138,1210,1296,1371,1446,1518,1595,1666,1788"}, "to": {"startLines": "66,67,68,69,70,125,126,244,245,247,248,252,254,255,256,257,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5215,5308,5388,5484,5579,9652,9730,20819,20910,21075,21157,21491,21641,21727,21802,21877,22050,22127,22198", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "5303,5383,5479,5574,5656,9725,9816,20905,20989,21152,21237,21558,21722,21797,21872,21944,22122,22193,22315"}}]}]}