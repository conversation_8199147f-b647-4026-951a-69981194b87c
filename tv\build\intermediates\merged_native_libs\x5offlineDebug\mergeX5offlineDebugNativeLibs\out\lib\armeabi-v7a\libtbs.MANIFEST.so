{"VERSIONCODE": "046238", "RESOURCE": "", "ENTRY": "tbs_jars_fusion_dex.jar:com.tencent.tbs.core.X5CoreEntryLegacy", "ENTRY2": {"DEX_LIST": "tbs_jars_fusion_dex.jar,pb_dex.jar", "ENTRY_CLASS": "com.tencent.tbs.core.X5CoreEntryLegacy"}, "FILES": [{"PATH": "pb_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs_jsapi_plugin.jar"}, {"PATH": "renderer_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs_bugly_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "native_ad.conf"}, {"PATH": "res.apk"}, {"PATH": "native_ad_plugin_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs_sdk_extension_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "apache_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs_jars_fusion_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs.conf"}, {"PATH": "tbs_ug_plugin.jar"}, {"PATH": "TencentLocationSDK.jar"}, {"PATH": "libtbsbeaconid.so"}, {"PATH": "libx5patch.so"}, {"PATH": "libmmkv.so"}, {"PATH": "libwebp_base.so"}, {"PATH": "libBugly-tbscore.so"}, {"PATH": "libzh-CN.so"}, {"PATH": "libqb_keystore.so"}, {"PATH": "libtbsqmp.so"}, {"PATH": "libmttwebview.so"}, {"PATH": "libresources.so"}, {"PATH": "libWxHevcDecoder.so"}, {"PATH": "libSharpPDecoder.so"}, {"PATH": "libchrome_100_percent.so"}, {"PATH": "liblinuxtoolsfortbssdk_jni.so"}, {"PATH": "libTPGDecoder.so"}, {"PATH": "libcmdsh.so"}, {"PATH": "libtbs_crash_handler.so"}, {"PATH": "libx5breakpad.so"}, {"PATH": "libtencentpos.so"}, {"PATH": "libMttPerfService.so"}, {"PATH": "libLzmaDec.so"}, {"PATH": "libtbslog.so"}, {"PATH": "libx5log.so"}], "BUILD_TYPE": "32", "NO_SEALED": true}