{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeX5offlineDebugResources-77:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,820,926,1033,1122,1223,1342,1427,1507,1598,1691,1786,1880,1980,2073,2168,2263,2354,2445,2530,2637,2748,2850,2958,3066,3176,3338,15392", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "815,921,1028,1117,1218,1337,1422,1502,1593,1686,1781,1875,1975,2068,2163,2258,2349,2440,2525,2632,2743,2845,2953,3061,3171,3333,3433,15473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "47,48,49,50,51,52,53,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3529,3626,3728,3827,3927,4037,4147,15869", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3621,3723,3822,3922,4032,4142,4262,15965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "168", "startColumns": "4", "startOffsets": "14950", "endColumns": "88", "endOffsets": "15034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,146,229", "endColumns": "90,82,84", "endOffsets": "141,224,309"}, "to": {"startLines": "46,183,184", "startColumns": "4,4,4", "startOffsets": "3438,16226,16309", "endColumns": "90,82,84", "endOffsets": "3524,16304,16389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6751,6821,6891,6963,7029,7106,7173,7274,7367", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "6816,6886,6958,7024,7101,7168,7269,7362,7432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "197,292,378,475,574,660,743,840,931,1018,1103,1193,1269,1354,1430,1509,1584,1660,1727", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "287,373,470,569,655,738,835,926,1013,1098,1188,1264,1349,1425,1504,1579,1655,1722,1835"}, "to": {"startLines": "54,55,56,57,58,109,110,169,170,171,172,174,175,176,177,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4267,4362,4448,4545,4644,8514,8597,15039,15130,15217,15302,15478,15554,15639,15715,15794,15970,16046,16113", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "4357,4443,4540,4639,4725,8592,8689,15125,15212,15297,15387,15549,15634,15710,15789,15864,16041,16108,16221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8694,8813,8934,9050,9166,9268,9365,9479,9613,9731,9883,9967,10068,10163,10263,10378,10508,10614,10753,10889,11020,11186,11313,11433,11557,11677,11773,11870,11990,12106,12206,12317,12426,12566,12711,12821,12924,13010,13104,13196,13312,13402,13491,13592,13672,13756,13857,13963,14055,14154,14242,14354,14455,14559,14678,14758,14858", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "8808,8929,9045,9161,9263,9360,9474,9608,9726,9878,9962,10063,10158,10258,10373,10503,10609,10748,10884,11015,11181,11308,11428,11552,11672,11768,11865,11985,12101,12201,12312,12421,12561,12706,12816,12919,13005,13099,13191,13307,13397,13486,13587,13667,13751,13852,13958,14050,14149,14237,14349,14450,14554,14673,14753,14853,14945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1804,1921,2042,2109,2195,2271,2345,2443,2543,2607,2671,2724,2782,2830,2891,2956,3018,3084,3154,3218,3279,3345,3398,3462,3540,3618,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1799,1916,2037,2104,2190,2266,2340,2438,2538,2602,2666,2719,2777,2825,2886,2951,3013,3079,3149,3213,3274,3340,3393,3457,3535,3613,3672,3743"}, "to": {"startLines": "2,11,15,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4730,4811,4894,4967,5066,5162,5236,5302,5398,5493,5559,5628,5695,5766,5884,6001,6122,6189,6275,6351,6425,6523,6623,6687,7437,7490,7548,7596,7657,7722,7784,7850,7920,7984,8045,8111,8164,8228,8306,8384,8443", "endLines": "10,14,18,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "330,516,695,4806,4889,4962,5061,5157,5231,5297,5393,5488,5554,5623,5690,5761,5879,5996,6117,6184,6270,6346,6420,6518,6618,6682,6746,7485,7543,7591,7652,7717,7779,7845,7915,7979,8040,8106,8159,8223,8301,8379,8438,8509"}}]}]}