{"VERSIONCODE": "046295", "RESOURCE": "", "ENTRY": "tbs_jars_fusion_dex.jar:com.tencent.tbs.core.X5CoreEntryLegacy", "ENTRY2": {"DEX_LIST": "tbs_jars_fusion_dex.jar,pb_dex.jar", "ENTRY_CLASS": "com.tencent.tbs.core.X5CoreEntryLegacy"}, "FILES": [{"PATH": "tbs.conf"}, {"PATH": "native_ad_plugin_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs_ug_plugin.jar"}, {"PATH": "apache_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "native_ad.conf"}, {"PATH": "res.apk"}, {"PATH": "tbs_jsapi_plugin.jar"}, {"PATH": "tbs_sdk_extension_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs_bugly_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "TencentLocationSDK.jar"}, {"PATH": "pb_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "renderer_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "tbs_jars_fusion_dex.jar", "SEALED": ["com.tencent", "com.taf"]}, {"PATH": "libqb_keystore.so"}, {"PATH": "libtencentpos.so"}, {"PATH": "libresources.so"}, {"PATH": "libtbs_crash_handler.so"}, {"PATH": "libmmkv.so"}, {"PATH": "libBugly-tbscore.so"}, {"PATH": "libLzmaDec.so"}, {"PATH": "libx5breakpad.so"}, {"PATH": "libTPGDecoder.so"}, {"PATH": "libcmdsh.so"}, {"PATH": "libx5patch.so"}, {"PATH": "libwebp_base.so"}, {"PATH": "libx5log.so"}, {"PATH": "libmttwebview.so"}, {"PATH": "libSharpPDecoder.so"}, {"PATH": "libturingtbs.so"}, {"PATH": "libtbslog.so"}, {"PATH": "libWxHevcDecoder.so"}, {"PATH": "libchrome_100_percent.so"}, {"PATH": "liblinuxtoolsfortbssdk_jni.so"}, {"PATH": "libzh-CN.so"}, {"PATH": "libtbsqimei.so"}], "BUILD_TYPE": "64", "NO_SEALED": true}