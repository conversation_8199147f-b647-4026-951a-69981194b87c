<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\jniLibs"/></dataSet><dataSet config="x5offline" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs"><file name="arm64-v8a/libtbs.1.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.1.so"/><file name="arm64-v8a/libtbs.apache_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.apache_dex.jar.so"/><file name="arm64-v8a/libtbs.libBugly-tbscore.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libBugly-tbscore.so.so"/><file name="arm64-v8a/libtbs.libchrome_100_percent.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libchrome_100_percent.so.so"/><file name="arm64-v8a/libtbs.libcmdsh.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libcmdsh.so.so"/><file name="arm64-v8a/libtbs.liblinuxtoolsfortbssdk_jni.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.liblinuxtoolsfortbssdk_jni.so.so"/><file name="arm64-v8a/libtbs.libLzmaDec.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libLzmaDec.so.so"/><file name="arm64-v8a/libtbs.libmmkv.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libmmkv.so.so"/><file name="arm64-v8a/libtbs.libmttwebview.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libmttwebview.so.so"/><file name="arm64-v8a/libtbs.libqb_keystore.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libqb_keystore.so.so"/><file name="arm64-v8a/libtbs.libresources.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libresources.so.so"/><file name="arm64-v8a/libtbs.libSharpPDecoder.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libSharpPDecoder.so.so"/><file name="arm64-v8a/libtbs.libtbslog.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libtbslog.so.so"/><file name="arm64-v8a/libtbs.libtbsqimei.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libtbsqimei.so.so"/><file name="arm64-v8a/libtbs.libtbs_crash_handler.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libtbs_crash_handler.so.so"/><file name="arm64-v8a/libtbs.libtencentpos.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libtencentpos.so.so"/><file name="arm64-v8a/libtbs.libTPGDecoder.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libTPGDecoder.so.so"/><file name="arm64-v8a/libtbs.libturingtbs.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libturingtbs.so.so"/><file name="arm64-v8a/libtbs.libwebp_base.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libwebp_base.so.so"/><file name="arm64-v8a/libtbs.libWxHevcDecoder.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libWxHevcDecoder.so.so"/><file name="arm64-v8a/libtbs.libx5breakpad.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libx5breakpad.so.so"/><file name="arm64-v8a/libtbs.libx5log.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libx5log.so.so"/><file name="arm64-v8a/libtbs.libx5patch.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libx5patch.so.so"/><file name="arm64-v8a/libtbs.libzh-CN.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.libzh-CN.so.so"/><file name="arm64-v8a/libtbs.MANIFEST.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.MANIFEST.so"/><file name="arm64-v8a/libtbs.native_ad.conf.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.native_ad.conf.so"/><file name="arm64-v8a/libtbs.native_ad_plugin_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.native_ad_plugin_dex.jar.so"/><file name="arm64-v8a/libtbs.pb_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.pb_dex.jar.so"/><file name="arm64-v8a/libtbs.renderer_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.renderer_dex.jar.so"/><file name="arm64-v8a/libtbs.res.apk.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.res.apk.so"/><file name="arm64-v8a/libtbs.tbs.conf.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.tbs.conf.so"/><file name="arm64-v8a/libtbs.tbs_bugly_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.tbs_bugly_dex.jar.so"/><file name="arm64-v8a/libtbs.tbs_jars_fusion_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.tbs_jars_fusion_dex.jar.so"/><file name="arm64-v8a/libtbs.tbs_jsapi_plugin.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.tbs_jsapi_plugin.jar.so"/><file name="arm64-v8a/libtbs.tbs_sdk_extension_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.tbs_sdk_extension_dex.jar.so"/><file name="arm64-v8a/libtbs.tbs_ug_plugin.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.tbs_ug_plugin.jar.so"/><file name="arm64-v8a/libtbs.TencentLocationSDK.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\arm64-v8a\libtbs.TencentLocationSDK.jar.so"/><file name="armeabi-v7a/libtbs.1.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.1.so"/><file name="armeabi-v7a/libtbs.apache_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.apache_dex.jar.so"/><file name="armeabi-v7a/libtbs.libBugly-tbscore.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libBugly-tbscore.so.so"/><file name="armeabi-v7a/libtbs.libchrome_100_percent.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libchrome_100_percent.so.so"/><file name="armeabi-v7a/libtbs.libcmdsh.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libcmdsh.so.so"/><file name="armeabi-v7a/libtbs.liblinuxtoolsfortbssdk_jni.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.liblinuxtoolsfortbssdk_jni.so.so"/><file name="armeabi-v7a/libtbs.libLzmaDec.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libLzmaDec.so.so"/><file name="armeabi-v7a/libtbs.libmmkv.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libmmkv.so.so"/><file name="armeabi-v7a/libtbs.libMttPerfService.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libMttPerfService.so.so"/><file name="armeabi-v7a/libtbs.libmttwebview.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libmttwebview.so.so"/><file name="armeabi-v7a/libtbs.libqb_keystore.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libqb_keystore.so.so"/><file name="armeabi-v7a/libtbs.libresources.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libresources.so.so"/><file name="armeabi-v7a/libtbs.libSharpPDecoder.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libSharpPDecoder.so.so"/><file name="armeabi-v7a/libtbs.libtbsbeaconid.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libtbsbeaconid.so.so"/><file name="armeabi-v7a/libtbs.libtbslog.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libtbslog.so.so"/><file name="armeabi-v7a/libtbs.libtbsqmp.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libtbsqmp.so.so"/><file name="armeabi-v7a/libtbs.libtbs_crash_handler.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libtbs_crash_handler.so.so"/><file name="armeabi-v7a/libtbs.libtencentpos.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libtencentpos.so.so"/><file name="armeabi-v7a/libtbs.libTPGDecoder.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libTPGDecoder.so.so"/><file name="armeabi-v7a/libtbs.libwebp_base.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libwebp_base.so.so"/><file name="armeabi-v7a/libtbs.libWxHevcDecoder.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libWxHevcDecoder.so.so"/><file name="armeabi-v7a/libtbs.libx5breakpad.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libx5breakpad.so.so"/><file name="armeabi-v7a/libtbs.libx5log.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libx5log.so.so"/><file name="armeabi-v7a/libtbs.libx5patch.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libx5patch.so.so"/><file name="armeabi-v7a/libtbs.libzh-CN.so.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.libzh-CN.so.so"/><file name="armeabi-v7a/libtbs.MANIFEST.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.MANIFEST.so"/><file name="armeabi-v7a/libtbs.native_ad.conf.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.native_ad.conf.so"/><file name="armeabi-v7a/libtbs.native_ad_plugin_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.native_ad_plugin_dex.jar.so"/><file name="armeabi-v7a/libtbs.pb_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.pb_dex.jar.so"/><file name="armeabi-v7a/libtbs.renderer_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.renderer_dex.jar.so"/><file name="armeabi-v7a/libtbs.res.apk.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.res.apk.so"/><file name="armeabi-v7a/libtbs.tbs.conf.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.tbs.conf.so"/><file name="armeabi-v7a/libtbs.tbs_bugly_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.tbs_bugly_dex.jar.so"/><file name="armeabi-v7a/libtbs.tbs_jars_fusion_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.tbs_jars_fusion_dex.jar.so"/><file name="armeabi-v7a/libtbs.tbs_jsapi_plugin.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.tbs_jsapi_plugin.jar.so"/><file name="armeabi-v7a/libtbs.tbs_sdk_extension_dex.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.tbs_sdk_extension_dex.jar.so"/><file name="armeabi-v7a/libtbs.tbs_ug_plugin.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.tbs_ug_plugin.jar.so"/><file name="armeabi-v7a/libtbs.TencentLocationSDK.jar.so" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offline\jniLibs\armeabi-v7a\libtbs.TencentLocationSDK.jar.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\debug\jniLibs"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\x5offlineDebug\jniLibs"/></dataSet></merger>