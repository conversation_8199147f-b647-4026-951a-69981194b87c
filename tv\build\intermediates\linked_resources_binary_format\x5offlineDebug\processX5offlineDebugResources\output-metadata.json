{"version": 3, "artifactType": {"type": "LINKED_RESOURCES_BINARY_FORMAT", "kind": "Directory"}, "applicationId": "com.github.mytv.android.x5offline", "variantName": "x5offlineDebug", "elements": [{"type": "UNIVERSAL", "filters": [], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-x5offlineUniversalDebug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "armeabi-v7a"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-x5offlineArmeabi-v7aDebug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86_64"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-x5offlineX86_64Debug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-x5offlineX86Debug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "arm64-v8a"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-x5offlineArm64-v8aDebug.ap_"}], "elementType": "File"}