{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeX5offlineDebugResources-77:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "204", "startColumns": "4", "startOffsets": "17583", "endColumns": "87", "endOffsets": "17666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2098,2221,2344,2420,2501,2574,2657,2754,2851,2919,2983,3036,3094,3142,3203,3276,3342,3406,3483,3550,3608,3675,3727,3794,3885,3976,4031", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2093,2216,2339,2415,2496,2569,2652,2749,2846,2914,2978,3031,3089,3137,3198,3271,3337,3401,3478,3545,3603,3670,3722,3789,3880,3971,4026,4093"}, "to": {"startLines": "2,11,17,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,717,6169,6247,6325,6408,6497,6586,6669,6736,6830,6924,6993,7059,7124,7196,7323,7446,7569,7645,7726,7799,7882,7979,8076,8144,8870,8923,8981,9029,9090,9163,9229,9293,9370,9437,9495,9562,9614,9681,9772,9863,9918", "endLines": "10,16,22,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "377,712,1034,6242,6320,6403,6492,6581,6664,6731,6825,6919,6988,7054,7119,7191,7318,7441,7564,7640,7721,7794,7877,7974,8071,8139,8203,8918,8976,9024,9085,9158,9224,9288,9365,9432,9490,9557,9609,9676,9767,9858,9913,9980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1163,1227,1308,1372,1433,1544,1608,1676,1730,1799,1861,1915,2026,2087,2149,2203,2275,2404,2493,2572,2667,2752,2834,2983,3065,3148,3285,3372,3449,3503,3554,3620,3691,3767,3838,3921,3998,4076,4154,4230,4338,4428,4501,4596,4693,4765,4839,4939,4991,5076,5142,5230,5320,5382,5446,5509,5580,5687,5799,5898,6005,6063,6118,6194,6278,6355", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1158,1222,1303,1367,1428,1539,1603,1671,1725,1794,1856,1910,2021,2082,2144,2198,2270,2399,2488,2567,2662,2747,2829,2978,3060,3143,3280,3367,3444,3498,3549,3615,3686,3762,3833,3916,3993,4071,4149,4225,4333,4423,4496,4591,4688,4760,4834,4934,4986,5071,5137,5225,5315,5377,5441,5504,5575,5682,5794,5893,6000,6058,6113,6189,6273,6350,6428"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,77,78,129,130,133,191,192,193,194,195,196,197,198,199,200,201,202,203,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,252,256,257,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1039,4120,4195,4270,4349,4453,5285,5370,5487,6040,6105,9985,10066,10300,16612,16723,16787,16855,16909,16978,17040,17094,17205,17266,17328,17382,17454,17671,17760,17839,17934,18019,18101,18250,18332,18415,18552,18639,18716,18770,18821,18887,18958,19034,19105,19188,19265,19343,19421,19497,19605,19695,19768,19863,19960,20032,20106,20206,20258,20343,20409,20497,20587,20649,20713,20776,20847,20954,21066,21165,21272,21330,21558,21887,21971,22120", "endLines": "28,57,58,59,60,61,69,70,71,77,78,129,130,133,191,192,193,194,195,196,197,198,199,200,201,202,203,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,252,256,257,259", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "1320,4190,4265,4344,4448,4543,5365,5482,5564,6100,6164,10061,10125,10356,16718,16782,16850,16904,16973,17035,17089,17200,17261,17323,17377,17449,17578,17755,17834,17929,18014,18096,18245,18327,18410,18547,18634,18711,18765,18816,18882,18953,19029,19100,19183,19260,19338,19416,19492,19600,19690,19763,19858,19955,20027,20101,20201,20253,20338,20404,20492,20582,20644,20708,20771,20842,20949,21061,21160,21267,21325,21380,21629,21966,22043,22193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4650,4737,4824,4936,5016,5103,5198,5303,5394,5503,5591,5697,5798,5908,6026,6106,6209", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4645,4732,4819,4931,5011,5098,5193,5298,5389,5498,5586,5692,5793,5903,6021,6101,6204,6301"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10361,10476,10593,10715,10830,10930,11029,11145,11283,11405,11547,11631,11730,11822,11918,12035,12159,12263,12403,12539,12683,12844,12976,13097,13222,13343,13436,13536,13656,13780,13879,13983,14089,14230,14377,14488,14587,14661,14756,14852,14956,15043,15130,15242,15322,15409,15504,15609,15700,15809,15897,16003,16104,16214,16332,16412,16515", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "10471,10588,10710,10825,10925,11024,11140,11278,11400,11542,11626,11725,11817,11913,12030,12154,12258,12398,12534,12678,12839,12971,13092,13217,13338,13431,13531,13651,13775,13874,13978,14084,14225,14372,14483,14582,14656,14751,14847,14951,15038,15125,15237,15317,15404,15499,15604,15695,15804,15892,15998,16099,16209,16327,16407,16510,16607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,226", "endColumns": "82,87,87", "endOffsets": "133,221,309"}, "to": {"startLines": "56,268,269", "startColumns": "4,4,4", "startOffsets": "4037,22883,22971", "endColumns": "82,87,87", "endOffsets": "4115,22966,23054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8208,8281,8342,8404,8473,8551,8621,8714,8805", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "8276,8337,8399,8468,8546,8616,8709,8800,8865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1440,1542,1650,1736,1843,1962,2041,2117,2208,2301,2396,2490,2591,2684,2779,2874,2965,3056,3138,3247,3347,3446,3555,3667,3778,3941,21804", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "1435,1537,1645,1731,1838,1957,2036,2112,2203,2296,2391,2485,2586,2679,2774,2869,2960,3051,3133,3242,3342,3441,3550,3662,3773,3936,4032,21882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "62,63,64,65,66,67,68,264", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4548,4645,4747,4845,4944,5058,5163,22512", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "4640,4742,4840,4939,5053,5158,5280,22608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189,284,369,478,583,660,737,830,920,1003,1086,1173,1245,1329,1405,1483,1559,1641,1709", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "279,364,473,578,655,732,825,915,998,1081,1168,1240,1324,1400,1478,1554,1636,1704,1824"}, "to": {"startLines": "72,73,74,75,76,131,132,250,251,253,254,258,260,261,262,263,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5569,5664,5749,5858,5963,10130,10207,21385,21475,21634,21717,22048,22198,22282,22358,22436,22613,22695,22763", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "5659,5744,5853,5958,6035,10202,10295,21470,21553,21712,21799,22115,22277,22353,22431,22507,22690,22758,22878"}}]}]}