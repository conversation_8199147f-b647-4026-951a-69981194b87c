{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeX5offlineDebugResources-77:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1784,1903,2014,2086,2165,2236,2306,2388,2468,2532,2595,2648,2706,2754,2815,2876,2943,3005,3071,3130,3195,3260,3313,3373,3447,3521,3574", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,52,59,73,73,52,64", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1779,1898,2009,2081,2160,2231,2301,2383,2463,2527,2590,2643,2701,2749,2810,2871,2938,3000,3066,3125,3190,3255,3308,3368,3442,3516,3569,3634"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,582,5728,5810,5890,5973,6060,6154,6222,6286,6376,6467,6532,6600,6660,6728,6841,6960,7071,7143,7222,7293,7363,7445,7525,7589,8328,8381,8439,8487,8548,8609,8676,8738,8804,8863,8928,8993,9046,9106,9180,9254,9307", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,52,59,73,73,52,64", "endOffsets": "376,577,761,5805,5885,5968,6055,6149,6217,6281,6371,6462,6527,6595,6655,6723,6836,6955,7066,7138,7217,7288,7358,7440,7520,7584,7647,8376,8434,8482,8543,8604,8671,8733,8799,8858,8923,8988,9041,9101,9175,9249,9302,9367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,355,452,550,637,723,808,897,980,1060,1145,1216,1300,1376,1452,1528,1604,1670", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "268,350,447,545,632,718,803,892,975,1055,1140,1211,1295,1371,1447,1523,1599,1665,1783"}, "to": {"startLines": "66,67,68,69,70,125,126,244,245,247,248,252,254,255,256,257,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5150,5244,5326,5423,5521,9528,9614,20699,20788,20949,21029,21356,21500,21584,21660,21736,21913,21989,22055", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "5239,5321,5418,5516,5603,9609,9694,20783,20866,21024,21109,21422,21579,21655,21731,21807,21984,22050,22168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "50,262,263", "startColumns": "4,4,4", "startOffsets": "3615,22173,22258", "endColumns": "72,84,84", "endOffsets": "3683,22253,22338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7652,7722,7787,7856,7925,8000,8064,8161,8255", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "7717,7782,7851,7920,7995,8059,8156,8250,8323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,258", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4120,4220,4324,4425,4528,4630,4735,21812", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "4215,4319,4420,4523,4625,4730,4847,21908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "16940", "endColumns": "87", "endOffsets": "17023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1036,1142,1249,1339,1440,1552,1630,1707,1798,1891,1984,2081,2181,2274,2369,2463,2554,2645,2725,2832,2933,3030,3139,3241,3355,3512,21114", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "1031,1137,1244,1334,1435,1547,1625,1702,1793,1886,1979,2076,2176,2269,2364,2458,2549,2640,2720,2827,2928,3025,3134,3236,3350,3507,3610,21189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4687,4772,4862,4962,5042,5125,5224,5323,5420,5519,5606,5710,5810,5914,6032,6112,6212", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4682,4767,4857,4957,5037,5120,5219,5318,5415,5514,5601,5705,5805,5909,6027,6107,6207,6301"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9758,9883,10006,10117,10242,10345,10445,10560,10696,10819,10965,11050,11156,11247,11345,11459,11589,11700,11835,11969,12097,12275,12400,12516,12635,12760,12852,12947,13067,13196,13296,13399,13508,13645,13787,13902,14000,14076,14179,14283,14390,14475,14565,14665,14745,14828,14927,15026,15123,15222,15309,15413,15513,15617,15735,15815,15915", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "9878,10001,10112,10237,10340,10440,10555,10691,10814,10960,11045,11151,11242,11340,11454,11584,11695,11830,11964,12092,12270,12395,12511,12630,12755,12847,12942,13062,13191,13291,13394,13503,13640,13782,13897,13995,14071,14174,14278,14385,14470,14560,14660,14740,14823,14922,15021,15118,15217,15304,15408,15508,15612,15730,15810,15910,16004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1046,1109,1200,1265,1324,1412,1474,1536,1596,1663,1726,1780,1894,1951,2012,2066,2136,2255,2336,2413,2502,2584,2669,2804,2881,2958,3099,3185,3269,3325,3377,3443,3513,3591,3662,3744,3814,3890,3961,4030,4144,4240,4314,4412,4508,4582,4652,4754,4809,4897,4964,5051,5144,5207,5271,5334,5400,5500,5609,5703,5810,5870,5926,6004,6088,6166", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "254,339,426,509,602,686,786,902,984,1041,1104,1195,1260,1319,1407,1469,1531,1591,1658,1721,1775,1889,1946,2007,2061,2131,2250,2331,2408,2497,2579,2664,2799,2876,2953,3094,3180,3264,3320,3372,3438,3508,3586,3657,3739,3809,3885,3956,4025,4139,4235,4309,4407,4503,4577,4647,4749,4804,4892,4959,5046,5139,5202,5266,5329,5395,5495,5604,5698,5805,5865,5921,5999,6083,6161,6234"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,3688,3773,3860,3943,4036,4852,4952,5068,5608,5665,9372,9463,9699,16009,16097,16159,16221,16281,16348,16411,16465,16579,16636,16697,16751,16821,17028,17109,17186,17275,17357,17442,17577,17654,17731,17872,17958,18042,18098,18150,18216,18286,18364,18435,18517,18587,18663,18734,18803,18917,19013,19087,19185,19281,19355,19425,19527,19582,19670,19737,19824,19917,19980,20044,20107,20173,20273,20382,20476,20583,20643,20871,21194,21278,21427", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "920,3768,3855,3938,4031,4115,4947,5063,5145,5660,5723,9458,9523,9753,16092,16154,16216,16276,16343,16406,16460,16574,16631,16692,16746,16816,16935,17104,17181,17270,17352,17437,17572,17649,17726,17867,17953,18037,18093,18145,18211,18281,18359,18430,18512,18582,18658,18729,18798,18912,19008,19082,19180,19276,19350,19420,19522,19577,19665,19732,19819,19912,19975,20039,20102,20168,20268,20377,20471,20578,20638,20694,20944,21273,21351,21495"}}]}]}