{"version": 3, "artifactType": {"type": "APK", "kind": "Directory"}, "applicationId": "com.github.mytv.android.x5offline", "variantName": "x5offlineDebug", "elements": [{"type": "UNIVERSAL", "filters": [], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "mytv-android-tv-2.0.0.0-all-sdk21-x5offline.apk"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "armeabi-v7a"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "mytv-android-tv-2.0.0.0-armeabi-v7a-sdk21-x5offline.apk"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86_64"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "mytv-android-tv-2.0.0.0-x86_64-sdk21-x5offline.apk"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "mytv-android-tv-2.0.0.0-x86-sdk21-x5offline.apk"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "arm64-v8a"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "mytv-android-tv-2.0.0.0-arm64-v8a-sdk21-x5offline.apk"}], "elementType": "File", "minSdkVersionForDexing": 21}