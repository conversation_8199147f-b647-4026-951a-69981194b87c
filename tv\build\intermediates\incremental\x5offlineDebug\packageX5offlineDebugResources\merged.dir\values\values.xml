<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">电视直播</string>
    <string name="cloud_sync_apply_success">应用云端数据成功</string>
    <string name="cloud_sync_auto_pull">自动拉取</string>
    <string name="cloud_sync_auto_pull_desc">应用启动时自动拉取云端数据并应用</string>
    <string name="cloud_sync_data">云端数据</string>
    <string name="cloud_sync_data_long_press">长按应用当前云端数据</string>
    <string name="cloud_sync_description">备注</string>
    <string name="cloud_sync_gitee_gist_id">Gitee 代码片段 Id</string>
    <string name="cloud_sync_gitee_gist_token">Gitee 代码片段 Token</string>
    <string name="cloud_sync_github_gist_id">Github Gist Id</string>
    <string name="cloud_sync_github_gist_token">Github Gist Token</string>
    <string name="cloud_sync_local_file_path">本地文件路径</string>
    <string name="cloud_sync_network_url">网络链接</string>
    <string name="cloud_sync_no_data">无云端数据</string>
    <string name="cloud_sync_provider">云同步服务商</string>
    <string name="cloud_sync_pull">拉取云端</string>
    <string name="cloud_sync_pull_failed">拉取云端失败</string>
    <string name="cloud_sync_push">推送云端</string>
    <string name="cloud_sync_push_device">推送设备</string>
    <string name="cloud_sync_push_failed">推送云端失败</string>
    <string name="cloud_sync_push_success">推送云端成功</string>
    <string name="cloud_sync_push_time">推送时间</string>
    <string name="cloud_sync_version">云端版本</string>
    <string name="cloud_sync_webdav_password">WebDAV 密码</string>
    <string name="cloud_sync_webdav_url">WebDAV 地址</string>
    <string name="cloud_sync_webdav_username">WebDAV 用户名</string>
    <string name="codec_detail_audio_bitrate_range_title">音频码率范围</string>
    <string name="codec_detail_color_formats_title">颜色格式</string>
    <string name="codec_detail_hs_hardware">硬解</string>
    <string name="codec_detail_hs_software">软解</string>
    <string name="codec_detail_hs_title">软硬解码</string>
    <string name="codec_detail_max_supported_instances_title">最大并发编解码器实例数量</string>
    <string name="codec_detail_video_frame_achievable_title">可实现的视频帧率（可能无数据）</string>
    <string name="codec_detail_video_frame_range_title">视频帧率范围</string>
    <string name="codec_detail_video_frame_supported_title">支持的视频帧率</string>
    <string name="codec_detail_video_frame_unsupported">不受支持</string>
    <string name="codec_detail_video_max_bitrate_title">最大视频码率</string>
    <string name="codec_detail_video_resolution_1080p">1080P</string>
    <string name="codec_detail_video_resolution_1440p">2K</string>
    <string name="codec_detail_video_resolution_2160p">4K</string>
    <string name="codec_detail_video_resolution_360p">360P</string>
    <string name="codec_detail_video_resolution_4320p">8K</string>
    <string name="codec_detail_video_resolution_480p">480P</string>
    <string name="codec_detail_video_resolution_720p">720P</string>
    <string name="codec_detail_video_resolution_unknown">未知</string>
    <string name="iptv_channel_favorite_enable">启用收藏</string>
    <string name="iptv_channel_favorite_enable_desc">是否显示当前订阅源频道收藏列表</string>
    <string name="iptv_channel_recent_enable">启用最近观看</string>
    <string name="iptv_channel_recent_enable_desc">是否显示最近观看的频道列表</string>
    <string name="iptv_source_local">本地</string>
    <string name="iptv_source_remote">远程</string>
    <string name="ui_Density_Ratio_adaptive">自适应</string>
    <string name="ui_about_app_id">应用标识</string>
    <string name="ui_about_check_update">检查更新</string>
    <string name="ui_about_device_id">设备ID</string>
    <string name="ui_about_device_name">设备名称</string>
    <string name="ui_about_origin_repo">天光云影仓库</string>
    <string name="ui_about_origin_repo_qrcode_desc">本项目的诞生离不开天光云影项目，扫码以前往</string>
    <string name="ui_about_origin_reward">向天光云影作者赞赏</string>
    <string name="ui_about_origin_reward_support">仅支持微信赞赏码</string>
    <string name="ui_about_repo">本项目代码仓库</string>
    <string name="ui_about_repo_qrcode_desc">扫码前往代码仓库</string>
    <string name="ui_about_telegram">讨论交流 Telegram</string>
    <string name="ui_about_update_new">新版本: %1$s</string>
    <string name="ui_about_update_none">无更新</string>
    <string name="ui_add_other_iptv_sources">添加其他订阅源</string>
    <string name="ui_add_other_playlists">添加其他节目单</string>
    <string name="ui_agreement_agree">已阅读并同意</string>
    <string name="ui_agreement_disagree">退出应用</string>
    <string name="ui_agreement_title">使用须知</string>
    <string name="ui_app_exit">再按一次退出</string>
    <string name="ui_auto_add_web_source">自动添加网页源</string>
    <string name="ui_auto_add_web_source_desc">为订阅源中的频道自动添加对应的网页源线路</string>
    <string name="ui_cache_cleared">缓存已清除</string>
    <string name="ui_channel_alias">频道别名</string>
    <string name="ui_channel_alias_count">共%1$d个频道，%2$d个别名</string>
    <string name="ui_channel_change_cross_group">频道切换跨分组</string>
    <string name="ui_channel_change_cross_group_desc">启用后，上下键可在所有频道间切换；关闭则仅在当前分组内切换</string>
    <string name="ui_channel_count">共%d个频道</string>
    <string name="ui_channel_empty">尚未加载列表</string>
    <string name="ui_channel_group_all">全部</string>
    <string name="ui_channel_group_count">共%1$d个分组</string>
    <string name="ui_channel_group_count_hidden">共%1$d个分组，已隐藏%2$d个分组</string>
    <string name="ui_channel_group_manage">频道分组管理</string>
    <string name="ui_channel_info_exit_live">再按一次退出直播</string>
    <string name="ui_channel_info_favorite_add">已收藏：</string>
    <string name="ui_channel_info_favorite_cancel">取消收藏：</string>
    <string name="ui_channel_info_favorite_clear">已清空所有收藏</string>
    <string name="ui_channel_info_load_failed">加载失败</string>
    <string name="ui_channel_info_loading">加载中...</string>
    <string name="ui_channel_info_next_play">稍后播放</string>
    <string name="ui_channel_info_now_play">正在播放</string>
    <string name="ui_channel_info_now_replay">正在回放</string>
    <string name="ui_channel_info_ready">就绪</string>
    <string name="ui_channel_info_replay">回放</string>
    <string name="ui_channel_info_reserve">预约</string>
    <string name="ui_channel_info_reserved">已预约</string>
    <string name="ui_channel_info_time_shift">时移</string>
    <string name="ui_channel_info_timeout">超时</string>
    <string name="ui_channel_info_update_checking">正在检查更新...</string>
    <string name="ui_channel_info_update_found">发现新版本: </string>
    <string name="ui_channel_info_update_latest">当前已是最新版本</string>
    <string name="ui_channel_list_loop">频道列表首尾循环</string>
    <string name="ui_channel_list_loop_desc">启用后，到达列表首尾时将循环切换到另一端</string>
    <string name="ui_channel_logo_override">频道图标覆盖</string>
    <string name="ui_channel_logo_override_desc">使用频道图标提供覆盖订阅源中定义的频道图标</string>
    <string name="ui_channel_logo_provider">频道图标提供</string>
    <string name="ui_channel_no_select">数字选台</string>
    <string name="ui_channel_no_select_desc">通过数字键选择频道</string>
    <string name="ui_channel_view_audio_track">音轨</string>
    <string name="ui_channel_view_auto_decode">自动解码</string>
    <string name="ui_channel_view_beta">测试版</string>
    <string name="ui_channel_view_boot_start">开机自启</string>
    <string name="ui_channel_view_boot_start_live">打开直接进入直播</string>
    <string name="ui_channel_view_boot_start_support">请确保当前设备支持该功能</string>
    <string name="ui_channel_view_clear_cache">清除缓存</string>
    <string name="ui_channel_view_clear_cache_support">约</string>
    <string name="ui_channel_view_cloud_sync">云同步</string>
    <string name="ui_channel_view_control">控制</string>
    <string name="ui_channel_view_debug">调试</string>
    <string name="ui_channel_view_delete">删除</string>
    <string name="ui_channel_view_dev">开发版</string>
    <string name="ui_channel_view_display_mode">显示模式</string>
    <string name="ui_channel_view_epg">节目单</string>
    <string name="ui_channel_view_force_remind">更新强提醒</string>
    <string name="ui_channel_view_force_remind_off">检测到新版本时仅消息提示</string>
    <string name="ui_channel_view_force_remind_on">检测到新版本时会全屏提醒</string>
    <string name="ui_channel_view_force_soft_decode">强制软解</string>
    <string name="ui_channel_view_general">通用</string>
    <string name="ui_channel_view_home">主页</string>
    <string name="ui_channel_view_interface">界面</string>
    <string name="ui_channel_view_log">日志</string>
    <string name="ui_channel_view_network">网络</string>
    <string name="ui_channel_view_permissions">权限</string>
    <string name="ui_channel_view_picture_in_picture">画中画</string>
    <string name="ui_channel_view_playback_control">播放控制</string>
    <string name="ui_channel_view_player">播放器</string>
    <string name="ui_channel_view_player1">播放器：</string>
    <string name="ui_channel_view_refresh">刷新</string>
    <string name="ui_channel_view_restore_initialization">恢复初始化</string>
    <string name="ui_channel_view_restore_initialization_support">已恢复初始化</string>
    <string name="ui_channel_view_route">线路</string>
    <string name="ui_channel_view_schedule">向右查看节目单</string>
    <string name="ui_channel_view_set_current">设为当前</string>
    <string name="ui_channel_view_source">订阅源</string>
    <string name="ui_channel_view_stable">稳定版</string>
    <string name="ui_channel_view_subscription_left">向左查看订阅源</string>
    <string name="ui_channel_view_subtitle">字幕</string>
    <string name="ui_channel_view_theme">主题</string>
    <string name="ui_channel_view_update">更新</string>
    <string name="ui_channel_view_update_channel">更新通道</string>
    <string name="ui_channel_view_video_track">视轨</string>
    <string name="ui_channel_view_webview_player">WebView</string>
    <string name="ui_classic_show_all_channels">显示全部频道</string>
    <string name="ui_classic_show_all_channels_desc">是否显示当前订阅源全部频道列表</string>
    <string name="ui_classic_show_channel_info">显示频道信息</string>
    <string name="ui_classic_show_channel_info_desc">在经典选台界面中显示当前频道的详细信息</string>
    <string name="ui_classic_show_source_list">显示订阅源列表</string>
    <string name="ui_classic_show_source_list_desc">在经典选台界面中启用"向左查看订阅源"功能</string>
    <string name="ui_close">关闭</string>
    <string name="ui_close_not">不关闭</string>
    <string name="ui_cloud_sync_pull">拉取云端数据</string>
    <string name="ui_control_action_settings">按键（手势）行为</string>
    <string name="ui_control_action_settings_desc">自定义播放界面的按键/手势行为</string>
    <string name="ui_control_down">下键/下滑</string>
    <string name="ui_control_left">左键/左滑</string>
    <string name="ui_control_long_down">长按下键</string>
    <string name="ui_control_long_left">长按左键</string>
    <string name="ui_control_long_right">长按右键</string>
    <string name="ui_control_long_select">长按选择键</string>
    <string name="ui_control_long_up">长按上键</string>
    <string name="ui_control_right">右键/右滑</string>
    <string name="ui_control_select">选择键</string>
    <string name="ui_control_up">上键/上滑</string>
    <string name="ui_convert_js">转换JS</string>
    <string name="ui_crash_handler_app_crashed">应用崩溃了</string>
    <string name="ui_crash_handler_copy_log">复制日志</string>
    <string name="ui_crash_handler_crash_log">崩溃日志</string>
    <string name="ui_crash_handler_crash_log_copied">崩溃日志已复制到剪贴板</string>
    <string name="ui_crash_handler_restart">重启</string>
    <string name="ui_crash_handler_tip">提示：您可以点击上方的\"复制日志\"按钮复制完整崩溃信息，以便报告问题。</string>
    <string name="ui_custom_subscription_source">自定义订阅源</string>
    <string name="ui_dashboard_module_about">关于</string>
    <string name="ui_dashboard_module_all_channels">全部频道</string>
    <string name="ui_dashboard_module_favorites">收藏</string>
    <string name="ui_dashboard_module_list_title">导航</string>
    <string name="ui_dashboard_module_live">直播</string>
    <string name="ui_dashboard_module_multi_view">多屏同播</string>
    <string name="ui_dashboard_module_push">推送</string>
    <string name="ui_dashboard_module_recently_watched">最近观看</string>
    <string name="ui_dashboard_module_search">搜索</string>
    <string name="ui_dashboard_module_settings">设置</string>
    <string name="ui_debug_decoder_info">解码器信息</string>
    <string name="ui_debug_decoder_info_desc">查看系统解码器</string>
    <string name="ui_debug_show_fps">显示FPS</string>
    <string name="ui_debug_show_fps_desc">在屏幕左上角显示fps和柱状图</string>
    <string name="ui_debug_show_layout_grids">显示布局网格</string>
    <string name="ui_debug_show_player_metadata">显示播放器信息</string>
    <string name="ui_debug_show_player_metadata_desc">显示播放器详细信息（编码、解码器、采样率等）</string>
    <string name="ui_density_scale_ratio">界面整体缩放比例</string>
    <string name="ui_density_scale_ratio_auto">自适应</string>
    <string name="ui_epg_enable">节目单启用</string>
    <string name="ui_epg_enable_desc">首次加载时可能会较为缓慢</string>
    <string name="ui_epg_item_day_after_tomorrow">后天</string>
    <string name="ui_epg_item_today">今天</string>
    <string name="ui_epg_item_tomorrow">明天</string>
    <string name="ui_epg_refresh_time_threshold">刷新时间阈值</string>
    <string name="ui_epg_refresh_time_threshold_desc">时间不到%1$d:00节目单将不会刷新</string>
    <string name="ui_epg_source_custom">自定义节目单</string>
    <string name="ui_epg_source_follow_iptv">跟随订阅源</string>
    <string name="ui_epg_source_follow_iptv_desc">优先使用订阅源中定义的节目单</string>
    <string name="ui_error_clipboard_service_unavailable">剪贴板服务不可用</string>
    <string name="ui_excellent_program">精彩节目</string>
    <string name="ui_favorites_clear">清空</string>
    <string name="ui_favorites_empty">没有收藏的频道</string>
    <string name="ui_focus_optimize">焦点优化</string>
    <string name="ui_focus_optimize_desc">关闭后可解决触摸设备在部分场景下闪退</string>
    <string name="ui_font_scale_ratio">界面字体缩放比例</string>
    <string name="ui_hybrid_mode_auto_add_web_source_to_back">将自动添加的网页源线路排在订阅源的后面</string>
    <string name="ui_hybrid_mode_auto_add_web_source_to_front">将自动添加的网页源线路排在订阅源的前面</string>
    <string name="ui_hybrid_mode_disable">禁用</string>
    <string name="ui_hybrid_mode_disable_auto_add_web_source">禁用自动添加网页源</string>
    <string name="ui_hybrid_mode_to_back">订阅源优先</string>
    <string name="ui_hybrid_mode_to_front">网页源优先</string>
    <string name="ui_hybrid_type">网页</string>
    <string name="ui_hybrid_type_cctv">央视网</string>
    <string name="ui_hybrid_type_official_site">官网</string>
    <string name="ui_hybrid_type_unknown">未知</string>
    <string name="ui_hybrid_type_webview">其它</string>
    <string name="ui_hybrid_type_yangshipin">央视频</string>
    <string name="ui_iptv_channel_epg_failed">节目单获取失败，请检查网络连接</string>
    <string name="ui_iptv_channel_hybrid">匹配可用的网页源</string>
    <string name="ui_iptv_channel_merge">合并相似频道</string>
    <string name="ui_iptv_hybrid_yangshipin_cookie">网页源央视频Cookie</string>
    <string name="ui_iptv_hybrid_yangshipin_cookie_desc">登录到央视频以收看付费频道</string>
    <string name="ui_iptv_pltv_to_tvod">PLTV转TVOD</string>
    <string name="ui_iptv_pltv_to_tvod_desc">自动将订阅源链接中的PLTV替换为TVOD以支持回看</string>
    <string name="ui_iptv_source_info">%1$d个分组,%2$d个频道,%3$d条源</string>
    <string name="ui_iptv_source_loading">加载订阅源</string>
    <string name="ui_keydown_action_channel_list">频道列表</string>
    <string name="ui_keydown_action_line_list">线路列表</string>
    <string name="ui_keydown_action_manage_sources">管理订阅源</string>
    <string name="ui_keydown_action_next_channel">后一频道</string>
    <string name="ui_keydown_action_next_line">后一线路</string>
    <string name="ui_keydown_action_playback_control">播放控制</string>
    <string name="ui_keydown_action_previous_channel">前一频道</string>
    <string name="ui_keydown_action_previous_line">前一线路</string>
    <string name="ui_keydown_action_program_list">节目单</string>
    <string name="ui_keydown_action_quick_settings">快捷设置</string>
    <string name="ui_minutes">分钟</string>
    <string name="ui_multi_view_channel_exists">已存在该频道</string>
    <string name="ui_multi_view_channel_minimum">至少保留一个频道</string>
    <string name="ui_multi_view_max_count_exceeded">超出最大添加频道数：</string>
    <string name="ui_multiview_action_add">添加</string>
    <string name="ui_multiview_action_delete">删除</string>
    <string name="ui_multiview_action_move_screen">移动屏幕</string>
    <string name="ui_multiview_action_move_to_screen">移动至屏幕</string>
    <string name="ui_multiview_action_mute">静音</string>
    <string name="ui_multiview_action_operate_screen">操作屏幕</string>
    <string name="ui_multiview_action_pause">暂停</string>
    <string name="ui_multiview_action_play">播放</string>
    <string name="ui_multiview_action_switch">切换</string>
    <string name="ui_multiview_action_unmute">取消静音</string>
    <string name="ui_multiview_action_zoom_in">放大</string>
    <string name="ui_multiview_action_zoom_out">缩小</string>
    <string name="ui_network_retry_count">HTTP请求重试次数</string>
    <string name="ui_network_retry_count_desc">影响订阅源、节目单数据获取</string>
    <string name="ui_network_retry_interval">HTTP请求重试间隔时间</string>
    <string name="ui_network_retry_interval_desc">影响订阅源、节目单数据获取</string>
    <string name="ui_permission_install_other_apps">安装未知应用</string>
    <string name="ui_permission_read_external_storage">读取外部存储/管理全部文件</string>
    <string name="ui_player_view_buffer_time">播放缓冲</string>
    <string name="ui_player_view_buffer_time_desc">对于Media3，为播放前的最小缓存加载时间（秒）;\n对于Ijk，为播放前的最小缓存加载帧（f）</string>
    <string name="ui_player_view_custom_headers">自定义headers</string>
    <string name="ui_player_view_display_mode">全局显示模式</string>
    <string name="ui_player_view_extract_header_from_link">在链接中提取 Header</string>
    <string name="ui_player_view_extract_header_from_link_desc">支持在链接中提取 以|分隔的 Header 信息</string>
    <string name="ui_player_view_fit_frame_rate">适配视频内容帧率</string>
    <string name="ui_player_view_fit_frame_rate_desc">启用后，播放器将尝试适配视频内容的帧率\n需要启用系统设置和渲染模式为SufaceView，系统版本大于 11\n在切换期间，你可能会观察到屏幕闪烁，部分视频源可能不适用或出现问题</string>
    <string name="ui_player_view_force_soft_decode">强制软解</string>
    <string name="ui_player_view_force_soft_decode_desc">对于Media3，使用设备和扩展软解码器;\n对于IJK，将禁用MediaCodec解码（使用ffmpeg）</string>
    <string name="ui_player_view_hls_allow_chunkless_preparation">HLS 允许无块准备</string>
    <string name="ui_player_view_hls_allow_chunkless_preparation_desc">允许Media3在播放 HLS 视频时启用无块准备以减少缓冲时间，启用此选项可能导致无法检测到嵌入的字幕</string>
    <string name="ui_player_view_load_timeout">加载超时</string>
    <string name="ui_player_view_load_timeout_desc">影响超时换源、断线重连</string>
    <string name="ui_player_view_playback_mode">回放方式</string>
    <string name="ui_player_view_player">视频播放器</string>
    <string name="ui_player_view_player_core">视频播放器内核</string>
    <string name="ui_player_view_render_mode">渲染方式</string>
    <string name="ui_player_view_rtsp_transport">RTSP 传输方式</string>
    <string name="ui_player_view_skip_multiple_frames_on_same_vsync">跳过多帧渲染</string>
    <string name="ui_player_view_stop_previous_media_item">停止上一媒体项</string>
    <string name="ui_player_view_support_ts_high_profile">支持 Media TS 高复杂度解析</string>
    <string name="ui_player_view_support_ts_high_profile_desc">支持在某些设备上使用 Media3 播放缺少 AUD 或 IDR 关键帧的 MPEG-TS 文件，启用该选项可能导致意外错误</string>
    <string name="ui_player_view_user_agent">全局UA</string>
    <string name="ui_player_view_webview_core">WebView内核</string>
    <string name="ui_push_scan_qr">请扫描二维码或输入IP地址进行连接</string>
    <string name="ui_push_service_started">服务已启动：</string>
    <string name="ui_refresh_all">刷新全部</string>
    <string name="ui_replace_system_webview">替换系统WebView</string>
    <string name="ui_replace_system_webview_desc">使用下包名为com.google.android.webview的应用替换系统WebView内核（重启生效）</string>
    <string name="ui_return">返回</string>
    <string name="ui_screen_auto_close_delay">超时自动关闭界面</string>
    <string name="ui_screen_auto_close_delay_never">不关闭</string>
    <string name="ui_search_keyword_hint">关键词...</string>
    <string name="ui_settings_language">语言</string>
    <string name="ui_settings_language_desc">选择应用界面语言</string>
    <string name="ui_settings_language_selected_tip">请重启应用以生效</string>
    <string name="ui_show_channel_logo">台标显示</string>
    <string name="ui_show_channel_preview">频道预览</string>
    <string name="ui_show_epg_programme_permanent_progress">常驻节目进度</string>
    <string name="ui_show_epg_programme_permanent_progress_desc">在播放器底部显示当前节目进度条</string>
    <string name="ui_show_epg_programme_progress">节目进度</string>
    <string name="ui_show_epg_programme_progress_desc">在频道底部显示当前节目进度条</string>
    <string name="ui_similar_channel_merge">相似频道合并</string>
    <string name="ui_similar_channel_merge_desc">相同频道别名将进行合并</string>
    <string name="ui_subscription_source_cache_time">订阅源缓存时间</string>
    <string name="ui_subscription_source_cache_time_forever">永久</string>
    <string name="ui_subscription_source_cache_time_none">不缓存</string>
    <string name="ui_theme_reset_to_default">恢复默认</string>
    <string name="ui_time_show_always">总是显示时间</string>
    <string name="ui_time_show_every_hour">整点前后%s秒显示时间</string>
    <string name="ui_time_show_half_hour">半点前后%s秒显示时间</string>
    <string name="ui_time_show_hidden">不显示时间</string>
    <string name="ui_time_show_mode">时间显示</string>
    <string name="ui_time_shows_always">常显</string>
    <string name="ui_time_shows_every_hour">整点</string>
    <string name="ui_time_shows_half_hour">半点</string>
    <string name="ui_time_shows_hidden">隐藏</string>
    <string name="ui_time_unit_days">天</string>
    <string name="ui_time_unit_hours">小时</string>
    <string name="ui_time_unit_minutes">分钟</string>
    <string name="ui_time_unit_seconds">秒</string>
    <string name="ui_update_ignore_and_back">忽略并返回</string>
    <string name="ui_update_is_latest">当前为最新版本（点击以返回）</string>
    <string name="ui_update_latest_version">最新版本: v%1$s</string>
    <string name="ui_update_now">立即更新</string>
    <string name="ui_update_updating">更新中，请勿关闭页面</string>
    <string name="ui_use_classic_panel_screen">经典选台界面</string>
    <string name="ui_use_classic_panel_screen_desc">将选台界面替换为经典三段式结构</string>
    <string name="ui_video_player_core_ijk_desc">部分视频（如加密的dash）可能无法正常使用</string>
    <string name="ui_video_player_core_media3_desc">除RTSP单播以外基本支持全部功能</string>
    <string name="ui_video_player_core_vlc_desc">VLC播放器，支持更多的字幕格式</string>
    <string name="ui_video_player_subtitle_background_color">背景颜色</string>
    <string name="ui_video_player_subtitle_edge_color">边框颜色</string>
    <string name="ui_video_player_subtitle_example">示例字幕</string>
    <string name="ui_video_player_subtitle_follow_embedded_style">跟随源嵌入样式</string>
    <string name="ui_video_player_subtitle_follow_embedded_style_desc">使用视频源中嵌入的字幕样式</string>
    <string name="ui_video_player_subtitle_foreground_color">字体颜色</string>
    <string name="ui_video_player_subtitle_settings">字幕设置</string>
    <string name="ui_video_player_subtitle_settings_desc">字幕样式调整</string>
    <string name="ui_video_player_subtitle_text_size">字体大小</string>
    <string name="ui_video_player_subtitle_use_system_style">使用系统样式</string>
    <string name="ui_video_player_subtitle_use_system_style_desc">使用Android系统（设置-无障碍）中设置的字体样式</string>
    <string name="ui_video_player_subtitle_window_color">窗口颜色</string>
    <string name="ui_video_player_track_closed">关闭</string>
    <string name="ui_video_player_webview_core_system_desc">系统自带内核</string>
    <string name="ui_video_player_webview_core_x5_desc">腾讯X5内核,仅支持armv7和arm64架构，第一次使用时需要初始化下载</string>
    <string name="ui_webview_load_timeout">WebView 加载超时</string>
    <string name="ui_webview_load_timeout_desc">WebView加载超时时间（秒）</string>
    <string name="ui_welcome_sentence0">欢迎使用</string>
    <string name="ui_welcome_sentence1">，请在使用前仔细阅读以下内容：</string>
    <string name="ui_welcome_sentence2">1. 本软件仅供学习交流使用，禁止用于任何商业用途，您不得二次编辑和修改本软件。</string>
    <string name="ui_welcome_sentence3">2. 本软件不提供任何直播内容，所有直播内容均来自网络。</string>
    <string name="ui_welcome_sentence4">3. 本软件完全基于您个人意愿使用，您应该对自己的使用行为和所有结果承担全部责任。</string>
    <string name="ui_welcome_sentence5">4. 如果本软件存在侵犯您的合法权益的情况，请及时与作者联系，作者将会及时删除有关内容。</string>
    <string name="ui_welcome_sentence6">如您继续使用本软件即代表您已完全理解并同意上述内容。</string>
    <string name="ui_x5_core_preload_arch_not_supported">X5不支持架构</string>
    <string name="ui_x5_core_preload_download_failure">获取X5Core失败，请使用系统WebView内核</string>
    <string name="ui_x5_core_preload_download_success">下载X5Core成功！</string>
    <string name="ui_x5_core_preload_downloading">正在远程下载X5Core，下载完成前请不要关闭应用</string>
    <string name="ui_x5_core_preload_failure">X5WebView内核加载失败</string>
    <string name="ui_x5_core_preload_not_supported">X5内核不可用，将进行初始化。已切换为系统内核</string>
    <string name="ui_x5_core_preload_success">X5内核加载成功,重启应用以生效</string>
    <style name="Theme.MyTV" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:windowBackground">@android:color/black</item>
        
        <item name="android:windowFullscreen">true</item>
    </style>
</resources>