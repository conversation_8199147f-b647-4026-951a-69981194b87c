#Wed Aug 20 14:25:23 HKT 2025
path.4=11/classes.dex
path.3=10/classes.dex
path.2=0/classes.dex
path.1=0/classes.dex
path.8=15/classes.dex
path.7=14/classes.dex
path.6=13/classes.dex
path.5=12/classes.dex
path.0=classes.dex
base.4=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\11\\classes.dex
base.3=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\10\\classes.dex
base.2=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\0\\classes.dex
base.1=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeLibDexX5offlineDebug\\0\\classes.dex
base.0=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeExtDexX5offlineDebug\\classes.dex
path.9=1/classes.dex
base.9=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\1\\classes.dex
base.8=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\15\\classes.dex
base.7=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\14\\classes.dex
base.6=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\13\\classes.dex
base.5=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\12\\classes.dex
renamed.21=classes22.dex
renamed.20=classes21.dex
renamed.18=classes19.dex
renamed.17=classes18.dex
renamed.9=classes10.dex
renamed.16=classes17.dex
path.18=classes1000.dex
renamed.8=classes9.dex
renamed.15=classes16.dex
path.19=classes2.dex
renamed.14=classes15.dex
renamed.13=classes14.dex
renamed.12=classes13.dex
renamed.11=classes12.dex
renamed.10=classes11.dex
base.17=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\9\\classes.dex
base.16=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\8\\classes.dex
base.15=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\7\\classes.dex
base.14=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\6\\classes.dex
base.19=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeExtDexX5offlineDebug\\classes2.dex
base.18=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\desugar_lib_dex\\x5offlineDebug\\l8DexDesugarLibX5offlineDebug\\classes1000.dex
base.20=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeExtDexX5offlineDebug\\classes3.dex
base.21=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeExtDexX5offlineDebug\\classes4.dex
renamed.3=classes4.dex
path.12=4/classes.dex
renamed.2=classes3.dex
path.13=5/classes.dex
renamed.1=classes2.dex
path.10=2/classes.dex
renamed.0=classes.dex
path.11=3/classes.dex
renamed.7=classes8.dex
path.16=8/classes.dex
renamed.6=classes7.dex
path.17=9/classes.dex
renamed.5=classes6.dex
path.14=6/classes.dex
renamed.4=classes5.dex
path.15=7/classes.dex
renamed.19=classes20.dex
base.13=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\5\\classes.dex
base.12=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\4\\classes.dex
path.20=classes3.dex
base.11=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\3\\classes.dex
base.10=C\:\\Users\\fly\\StudioProjects\\mytv-Internal\\tv\\build\\intermediates\\dex\\x5offlineDebug\\mergeProjectDexX5offlineDebug\\2\\classes.dex
path.21=classes4.dex
